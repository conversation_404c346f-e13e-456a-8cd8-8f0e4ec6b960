package com.gg.gapo.flutterx.internal.base

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import com.gg.gapo.core.ui.utils.EdgeToEdgeUtils
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.di.qualifier.GapoConstantQualifier
import com.gg.gapo.flutterx.internal.GapoFlutterEngineFactory
import com.gg.gapo.flutterx.internal.GapoFlutterEntryPoint
import com.gg.gapo.flutterx.internal.base.GapoFlutterBaseFragment.Companion.FLUTTER_ENGINE_ID_EXTRA
import com.gg.gapo.flutterx.internal.base.GapoFlutterBaseFragment.Companion.FLUTTER_ENTRY_POINT_EXTRA
import com.gg.gapo.flutterx.internal.base.GapoFlutterBaseFragment.Companion.FLUTTER_FRAGMENT_TAG
import io.flutter.embedding.android.FlutterFragment
import org.koin.core.qualifier.named
import org.koin.java.KoinJavaComponent
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 13/10/2021
 */
internal abstract class GapoFlutterBaseFragmentActivity : AppCompatActivity() {

    private var flutterFragment: FlutterFragment? = null

    private lateinit var engineId: String
    private lateinit var entryPoint: GapoFlutterEntryPoint

    override fun onCreate(savedInstanceState: Bundle?) {
        if (savedInstanceState != null &&
            savedInstanceState.containsKey(FLUTTER_ENGINE_ID_EXTRA) &&
            savedInstanceState.containsKey(FLUTTER_ENTRY_POINT_EXTRA)
        ) {
            engineId = savedInstanceState.getString(FLUTTER_ENGINE_ID_EXTRA)!!
            entryPoint = savedInstanceState.parcelable(FLUTTER_ENTRY_POINT_EXTRA)!!
            if (!GapoFlutterEngineFactory.isEngineExists(engineId)) {
                GapoFlutterEngineFactory.create(
                    this@GapoFlutterBaseFragmentActivity,
                    entryPoint,
                    engineId
                )
            }
        }
        super.onCreate(savedInstanceState)
    }

    override fun setContentView(layoutResID: Int) {
        super.setContentView(layoutResID)
        // Setup edge-to-edge after content view is set
        setupEdgeToEdge()
    }

    override fun setContentView(view: View?) {
        super.setContentView(view)
        // Setup edge-to-edge after content view is set
        setupEdgeToEdge()
    }

    override fun setContentView(view: View?, params: android.view.ViewGroup.LayoutParams?) {
        super.setContentView(view, params)
        // Setup edge-to-edge after content view is set
        setupEdgeToEdge()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (::engineId.isInitialized) {
            outState.putString(FLUTTER_ENGINE_ID_EXTRA, engineId)
        }
        if (::entryPoint.isInitialized) {
            outState.putParcelable(FLUTTER_ENTRY_POINT_EXTRA, entryPoint)
        }
    }

    override fun onResume() {
        val isFeatureCaptureScreenEnabled = try {
            KoinJavaComponent.getKoin().get<Boolean>(
                qualifier = named(GapoConstantQualifier.IS_ENABLE_CAPTURE_SCREEN)
            )
        } catch (e: Exception) {
            Timber.e(e)
            true
        }
        Timber.e("GapoThemeBaseActivity $isFeatureCaptureScreenEnabled")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            if (!isFeatureCaptureScreenEnabled) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                )
            } else {
                window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
            }
        }
        super.onResume()
    }

    override fun onPostResume() {
        super.onPostResume()
        flutterFragment?.onPostResume()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        flutterFragment?.onNewIntent(intent)
    }

    override fun onBackPressed() {
        flutterFragment?.onBackPressed() ?: super.onBackPressed()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        flutterFragment?.onRequestPermissionsResult(
            requestCode,
            permissions,
            grantResults
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        flutterFragment?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onUserLeaveHint() {
        flutterFragment?.onUserLeaveHint()
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        flutterFragment?.onTrimMemory(level)
    }

    override fun onDestroy() {
        if (::engineId.isInitialized) {
            GapoFlutterEngineFactory.clearPlugins(engineId)
        }
        super.onDestroy()
    }

    /**
     * Sets up edge-to-edge display for Android 15 compatibility.
     * Can be overridden by subclasses for custom behavior.
     */
    protected open fun setupEdgeToEdge() {
        if (shouldEnableEdgeToEdge()) {
            EdgeToEdgeUtils.setupActivityEdgeToEdge(this as ComponentActivity)
        } else {
            // Fallback for activities that need custom handling
            EdgeToEdgeUtils.setupDisplayCutoutMode(this)
        }
    }

    /**
     * Determines whether automatic edge-to-edge setup should be enabled.
     * Override this method to return false if your activity needs custom
     * edge-to-edge handling or window insets management.
     */
    protected open fun shouldEnableEdgeToEdge(): Boolean = true

    /**
     * Helper method to setup custom edge-to-edge with specific root view.
     * Call this from your activity's onCreate after setContentView if you
     * need custom insets handling for a specific view.
     */
    protected fun setupCustomEdgeToEdge(
        rootView: View,
        applySystemBarInsets: Boolean = true,
        applyImeInsets: Boolean = false,
        applyDisplayCutoutInsets: Boolean = true
    ) {
        EdgeToEdgeUtils.setupActivityEdgeToEdgeWithCustomRoot(
            activity = this as ComponentActivity,
            rootView = rootView,
            applySystemBarInsets = applySystemBarInsets,
            applyImeInsets = applyImeInsets,
            applyDisplayCutoutInsets = applyDisplayCutoutInsets
        )
    }

    fun commitFlutterFragment(
        fragmentClazz: Class<out GapoFlutterBaseFragment>,
        @IdRes id: Int,
        entryPoint: GapoFlutterEntryPoint,
        bundle: Bundle? = null
    ) {
        var flutterFragment =
            supportFragmentManager.findFragmentByTag(FLUTTER_FRAGMENT_TAG) as? FlutterFragment

        if (flutterFragment == null) {
            this.entryPoint = entryPoint
            this.engineId = GapoFlutterEngineFactory.create(
                this@GapoFlutterBaseFragmentActivity,
                entryPoint
            )
            flutterFragment = GapoFlutterBaseFragment.createInstance(
                fragmentClazz,
                engineId,
                entryPoint
            ).also {
                if (bundle != null) {
                    it.arguments?.putAll(bundle)
                }
                this.flutterFragment = it
            }
            supportFragmentManager.beginTransaction().add(id, flutterFragment, FLUTTER_FRAGMENT_TAG)
                .commitAllowingStateLoss()
        } else {
            this.flutterFragment = flutterFragment
            supportFragmentManager.beginTransaction().show(flutterFragment)
                .commitAllowingStateLoss()
        }
    }
}
