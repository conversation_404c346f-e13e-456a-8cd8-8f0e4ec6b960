package com.gg.gapo.core.ui.activity

import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.gg.gapo.core.ui.R
import com.gg.gapo.core.ui.utils.EdgeToEdgeUtils
import com.gg.gapo.core.utilities.di.qualifier.GapoConstantQualifier
import org.koin.android.ext.android.inject
import org.koin.core.qualifier.named
import org.koin.java.KoinJavaComponent
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 17/10/2023
 */

abstract class GapoThemeBaseActivity : AppCompatActivity() {

    private val wsName by inject<String>(qualifier = named(GapoConstantQualifier.WS_NAME))

    override fun onCreate(savedInstanceState: Bundle?) {
        if (wsName == Workspace.MOMOLIFE.wsName) {
            setTheme(R.style.MomoTheme)
            applicationContext.theme.applyStyle(R.style.MomoTheme, true)
        } else {
            setTheme(R.style.GapoTheme_NoActionBar)
            applicationContext.theme.applyStyle(R.style.GapoTheme_NoActionBar, true)
        }

        super.onCreate(savedInstanceState)

        // Setup edge-to-edge for Android 15 compatibility
        setupEdgeToEdge()
    }

    /**
     * Sets up edge-to-edge display for Android 15 compatibility.
     * Can be overridden by subclasses for custom behavior.
     */
    protected open fun setupEdgeToEdge() {
        EdgeToEdgeUtils.setupDisplayCutoutMode(this)
    }

    override fun onResume() {
        val isFeatureCaptureScreenEnabled = try {
            KoinJavaComponent.getKoin().get<Boolean>(
                qualifier = named(GapoConstantQualifier.IS_ENABLE_CAPTURE_SCREEN)
            )
        } catch (e: Exception) {
            Timber.e(e)
            true
        }
        Timber.e("GapoThemeBaseActivity $isFeatureCaptureScreenEnabled")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            if (!isFeatureCaptureScreenEnabled) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                )
            } else {
                window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
            }
        }
        super.onResume()
    }
}

internal enum class Workspace {
    MOMOLIFE {

        override val workspaceId: String?
            get() = null

        override val wsName: String
            get() = "momo"
    };

    abstract val workspaceId: String?
    abstract val wsName: String
}
