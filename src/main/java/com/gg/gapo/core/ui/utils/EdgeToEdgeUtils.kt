package com.gg.gapo.core.ui.utils

import android.app.Activity
import android.os.Build
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.enableEdgeToEdge
import androidx.core.graphics.Insets
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding

object EdgeToEdgeUtils {

    fun enableEdgeToEdge(activity: ComponentActivity) {
        activity.enableEdgeToEdge()
    }

    /**
     * Sets up window insets handling for a view with proper padding.
     * This is compatible with Android 15's edge-to-edge enforcement.
     */
    fun setupWindowInsets(
        view: View,
        applySystemBarInsets: Boolean = true,
        applyImeInsets: Boolean = false,
        applyDisplayCutoutInsets: Boolean = true
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            var leftPadding = 0
            var topPadding = 0
            var rightPadding = 0
            var bottomPadding = 0

            if (applySystemBarInsets) {
                val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
                leftPadding += systemBars.left
                topPadding += systemBars.top
                rightPadding += systemBars.right
                bottomPadding += systemBars.bottom
            }

            if (applyImeInsets) {
                val ime = insets.getInsets(WindowInsetsCompat.Type.ime())
                bottomPadding = maxOf(bottomPadding, ime.bottom)
            }

            if (applyDisplayCutoutInsets) {
                val displayCutout = insets.getInsets(WindowInsetsCompat.Type.displayCutout())
                leftPadding = maxOf(leftPadding, displayCutout.left)
                topPadding = maxOf(topPadding, displayCutout.top)
                rightPadding = maxOf(rightPadding, displayCutout.right)
                bottomPadding = maxOf(bottomPadding, displayCutout.bottom)
            }

            v.updatePadding(
                left = leftPadding,
                top = topPadding,
                right = rightPadding,
                bottom = bottomPadding
            )

            insets
        }
    }

    /**
     * Sets up window insets for system bars only (status bar + navigation bar).
     * Useful for most standard activities.
     */
    fun setupSystemBarsInsets(view: View) {
        setupWindowInsets(
            view = view,
            applySystemBarInsets = true,
            applyImeInsets = false,
            applyDisplayCutoutInsets = true
        )
    }

    /**
     * Sets up window insets for activities that need to handle keyboard (IME).
     * Useful for activities with input fields.
     */
    fun setupSystemBarsAndImeInsets(view: View) {
        setupWindowInsets(
            view = view,
            applySystemBarInsets = true,
            applyImeInsets = true,
            applyDisplayCutoutInsets = true
        )
    }

    /**
     * Sets up custom window insets handling with a callback.
     * Provides more control for complex layouts.
     */
    fun setupCustomWindowInsets(
        view: View,
        onInsetsChanged: (
            systemBars: Insets,
            ime: Insets,
            displayCutout: Insets
        ) -> Unit
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { _, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val ime = insets.getInsets(WindowInsetsCompat.Type.ime())
            val displayCutout = insets.getInsets(WindowInsetsCompat.Type.displayCutout())

            onInsetsChanged(systemBars, ime, displayCutout)

            insets
        }
    }

    /**
     * Ensures proper display cutout handling for Android 15.
     * This addresses the new LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS requirement.
     */
    fun setupDisplayCutoutMode(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            setupSystemBarsInsets
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // Pre-Android 15 - Manual setup for display cutout
            val layoutParams = activity.window.attributes
            layoutParams.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            activity.window.attributes = layoutParams
        }
    }

    /**
     * Helper method to check if the device is running Android 15 or higher.
     */
    fun isAndroid15OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM
    }

    /**
     * Helper method to apply insets to a view with margin instead of padding.
     * Useful when padding would interfere with the view's content.
     */
    fun setupWindowInsetsWithMargin(view: View) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val displayCutout = insets.getInsets(WindowInsetsCompat.Type.displayCutout())

            val leftMargin = maxOf(systemBars.left, displayCutout.left)
            val topMargin = maxOf(systemBars.top, displayCutout.top)
            val rightMargin = maxOf(systemBars.right, displayCutout.right)
            val bottomMargin = maxOf(systemBars.bottom, displayCutout.bottom)

            val layoutParams = v.layoutParams as? android.view.ViewGroup.MarginLayoutParams
            layoutParams?.setMargins(leftMargin, topMargin, rightMargin, bottomMargin)
            v.layoutParams = layoutParams

            insets
        }
    }
}
