package com.gg.gapo.livekit.call.presentation.logger

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.utils.EdgeToEdgeUtils
import com.gg.gapo.feature.call.R

@AppDeepLink(value = ["call_v2/logger"])
class CallLoggerActivity : GapoThemeBaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EdgeToEdgeUtils.enableEdgeToEdge(this)
        setContentView(R.layout.call_logger_activity)
        EdgeToEdgeUtils.setupSystemBarsInsets(findViewById(R.id.main))
    }
}
