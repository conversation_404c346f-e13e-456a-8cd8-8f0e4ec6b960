package com.gg.gapo.feature.messenger.presentation.messenger.viewmodel

import android.app.Application
import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.flatMap
import com.blankj.utilcode.util.UriUtils
import com.gg.gapo.core.auth.manager.AuthManager
import com.gg.gapo.core.mqtt.MqttService
import com.gg.gapo.core.navigation.deeplink.minitask.MiniTaskCreatorFromMessengerDeepLink
import com.gg.gapo.core.navigation.deeplink.photo.viewer.MediaViewerDeepLink
import com.gg.gapo.core.navigation.web.openWebBrowser
import com.gg.gapo.core.navigation.web.startPreviewBrowser
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.core.remoteconfig.GapoRemoteConfig
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.image.thumbpattern.GapoThumbPattern
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.di.env.AppEnvironment
import com.gg.gapo.core.utilities.file.isUnSupportedFileExt
import com.gg.gapo.core.utilities.link.GapoWorkLink
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.core.utilities.settings.GapoGeneralSettings.isUploadImageHighQualityEnabled
import com.gg.gapo.core.utilities.settings.GapoGeneralSettings.setUploadHighQualityEnabled
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.StatusMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.ReportViolationRequestBody
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationRole
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageBotCommandModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserParticipantsModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.QuickMessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageBodyType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageCarouselButtonType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.feature.messenger.domain.messenger.model.mqtt.MqttEventType
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.ConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchCollabByIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UpdateLastVisitAtConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UpdateMarkUnReadConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.AutoDeleteMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ChooseVoteUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CountAutoDeleteMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CreateVoteUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.DeleteMessageErrorUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.DeleteMessagesUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.EditMessageCacheActionUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchBotCommandsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchQuickMessagesUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchUsersByIdsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetLastMessageFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageRequestErrorUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessagesFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetSignedRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.JoinGroupCallUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.JumpMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.MarkUnreadMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReactMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReadMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReportViolationMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SaveMessageCreatorRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SendMessagesToSaveUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.UpdateReadMessageFromTypingEventUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin.PinedMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.ConnectedMessageMqttUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.user.UserUseCase
import com.gg.gapo.feature.messenger.filterStatusForMessenger
import com.gg.gapo.feature.messenger.logger.MessengerLogger
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.MessengerInteractor
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.MessengerMapper
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.MessengerMapperImpl
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.MessengerViewData
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.mapToRequest
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.reaction.MessengerReactType
import com.gg.gapo.feature.messenger.presentation.messenger.model.MessengerUserViewData
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnAddVote
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnAttachFile
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnCallAudio
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnCallGroup
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnCallVideo
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnClearedInputMessageLayout
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnClickErrorMessage
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnCreateMeeting
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnCreatePoll
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnDownloadFile
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnFinish
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnMessageCreateTask
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnMessageDeleteAction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnMessageForwardAction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenAvatarUserAction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenBotCommand
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenBotList
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenCamera
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenConversation
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenEmail
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenGallery
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenLink
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenLocalMedia
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenMedia
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenMentionUserAction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenMessageAction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenMessagesPinned
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenPhone
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenQuickMessagesPopUp
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenSelectedMedia
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenSetting
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenSubthread
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenUserProfile
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenUserReaction
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnOpenUserSeen
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnPauseVoice
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnPlayVoice
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnRecordVoice
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnScrollToBottom
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnScrollToMessageId
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnSendMessage
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnShowKeyboard
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnShowNegativeSnackBar
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnShowPositiveSnackBar
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnShowVotedUser
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnUnPinMessage
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnWarning
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewEvent.OnWatchMeetingFile
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.MessengerInputLayout
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext.mapToMessageMentionRequestModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.mapToMessageInputLayoutData
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.mapToMessengerInputLayoutData
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.mention.model.MessengerMentionUserViewData
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.preview.copyOrCreate
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.preview.mapToMessagePreviewLinkRequestModel
import com.gg.gapo.feature.messenger.utils.MessengerConstant
import com.gg.gapo.feature.messenger.utils.MessengerConstant.MAX_INPUT_MESSAGE
import com.gg.gapo.feature.messenger.utils.MessengerUtils
import com.gg.gapo.feature.messenger.utils.MessengerUtils.urlDecode
import com.gg.gapo.feature.messenger.utils.combineWith
import com.gg.gapo.feature.messenger.utils.getFileNameAndSizeByUri
import com.gg.gapo.feature.messenger.utils.isBot
import com.gg.gapo.feature.messenger.utils.isHttp
import com.gg.gapo.feature.messenger.utils.isUriVideo
import com.gg.gapo.feature.messenger.utils.isVideo
import com.gg.gapo.feature.messenger.utils.shortName
import com.gg.gapo.feature.messenger.worker.cancelDownloadFile
import com.gg.gapo.feature.messenger.worker.createMessage
import com.gg.gapo.feature.messenger.worker.createMessageWithQuickMessage
import com.gg.gapo.feature.messenger.worker.editMessage
import com.gg.gapo.messenger.data.sources.models.collab.CollabModel
import com.gg.gapo.shared.poll.vote.creator.model.PollVoteCreatorViewData
import com.google.gson.Gson
import com.simplemobiletools.commons.extensions.normalizeString
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.LinkedList
import java.util.concurrent.atomic.AtomicInteger

/**
 * <AUTHOR>
 * @since 07/07/2022
 */
internal class MessengerViewModel(
    private val application: Application,
    val appEnvironment: AppEnvironment,
    private val userManager: UserManager,
    private val authManager: AuthManager,
    val workspaceManager: WorkspaceManager,
    private val mqttService: MqttService,
    val gapoRemoteConfig: GapoRemoteConfig,
    private val coroutineDispatchers: CoroutineDispatchers,
    private val getMessagesFlowUseCase: GetMessagesFlowUseCase,
    private val jumpMessageUseCase: JumpMessageUseCase,
    private val connectedMessageMqttUseCase: ConnectedMessageMqttUseCase,
    private val saveMessageCreatorRequestUseCase: SaveMessageCreatorRequestUseCase,
    private val editMessageCacheActionUseCase: EditMessageCacheActionUseCase,
    private val reactMessageUseCase: ReactMessageUseCase,
    private val readMessageUseCase: ReadMessageUseCase,
    private val markUnreadMessageUseCase: MarkUnreadMessageUseCase,
    private val sendMessagesToSaveUseCase: SendMessagesToSaveUseCase,
    private val deleteMessagesUseCase: DeleteMessagesUseCase,
    private val chooseVoteUseCase: ChooseVoteUseCase,
    private val createVoteUseCase: CreateVoteUseCase,
    private val getLastMessageFlowUseCase: GetLastMessageFlowUseCase,
    private val getMessageRequestErrorUseCase: GetMessageRequestErrorUseCase,
    private val updateReadMessageFromTypingEventUseCase: UpdateReadMessageFromTypingEventUseCase,
    private val deleteMessageErrorUseCase: DeleteMessageErrorUseCase,
    private val conversationUseCase: ConversationUseCase,
    private val userUseCase: UserUseCase,
    private val pinedMessageUseCase: PinedMessageUseCase,
    private val updateLastVisitAtConversationUseCase: UpdateLastVisitAtConversationUseCase,
    private val updateMarkUnReadConversationUseCase: UpdateMarkUnReadConversationUseCase,
    private val fetchCollabByIdUseCase: FetchCollabByIdUseCase,
    private val messengerLogger: MessengerLogger,
    private val meetingFileUrl: String,
    private val conversationId: Long,
    private val autoDeleteMessageUseCase: AutoDeleteMessageUseCase,
    private val countAutoDeleteMessageUseCase: CountAutoDeleteMessageUseCase,
    private val onPremiseManager: OnPremiseManager,
    private val fetchBotCommandsUseCase: FetchBotCommandsUseCase,
    private val reportViolationMessageUseCase: ReportViolationMessageUseCase,
    private val joinGroupCallUseCase: JoinGroupCallUseCase,
    private val getSignedRequest: GetSignedRequestUseCase,
    private val fetchQuickMessagesUseCase: FetchQuickMessagesUseCase,
    private val fetchUsersByIdsUseCase: FetchUsersByIdsUseCase
) : ViewModel(), MessengerInteractor {

    override val brandName: String
        get() = onPremiseManager.onPremiseConfigData?.brandName.orEmpty()

    override val canSendMessage: LiveData<Boolean>
        get() = _canSendMessage

    private val _canSendMessage = MutableLiveData(true)
    override val currentWorkspaceId: String
        get() = workspaceManager.currentWorkspaceId

    val token: String
        get() = authManager.token

    var imagePathFromCamera: String? = null
    var userIdForCall: String = ""
    var userForCall: MessageUserModel? = null

    override val isVisibleMessengerInputLayout: LiveData<Boolean>
        get() = selectedMessageLiveData.combineWith(conversationLiveData) { selectedMessage, conversation ->
            return@combineWith selectedMessage to conversation
        }.map {
            val selectedMessage = it.first ?: return@map true
            val conversation = it.second ?: return@map true
            val canSendMessage = if (conversation.isSubthread) {
                _canSendMessage.value == true
            } else {
                conversation.isCanSendMessage()
            }
            return@map selectedMessage.isEmpty() && conversation.blockedBy.isNullOrEmpty() && canSendMessage && conversation.folderAlias != FolderType.Stranger && !(conversation.isDirect && conversation.partner?.isDeActive == true)
        }

    private val _contentLiveData = MutableLiveData<MessengerInputLayout.Data>()
    override val contentLiveData: LiveData<MessengerInputLayout.Data> = _contentLiveData

    private val _selectedMessageLiveData = MutableLiveData<List<MessageModel>>(emptyList())
    override val selectedMessageLiveData: LiveData<List<MessageModel>> = _selectedMessageLiveData

    private val _collabLiveData = MutableLiveData<CollabModel>()
    override val collabLiveData: LiveData<CollabModel> = _collabLiveData

    private val _lockSlidrEventLiveData = MutableLiveData(Event(false))
    val lockSlidrEventLiveData: LiveData<Event<Boolean>> = _lockSlidrEventLiveData

    private val _reportViolationEventLiveData = MutableLiveData<Event<Boolean>>()
    val reportViolationEventLiveData: LiveData<Event<Boolean>>
        get() = _reportViolationEventLiveData

    val isFeatureDownloadEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.DOWNLOAD) ?: true

    val isFeatureTaskEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.TASK) ?: true

    val isFeatureReportChatEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.REPORT_CHAT) ?: false

    private val _inputCreateTaskLiveData = MutableLiveData<Bundle>()
    val inputCreateTaskLiveData: LiveData<Bundle>
        get() = _inputCreateTaskLiveData

    private val _participantsLiveData = MutableLiveData<List<MessageUserParticipantsModel>>()
    val participantsLiveData: LiveData<List<MessageUserParticipantsModel>> = _participantsLiveData

    override val partnerProfile: LiveData<MessageUserModel?>
        get() = _partnerProfileLiveData
    private val _partnerProfileLiveData = MutableLiveData<MessageUserModel>()

    private val quickMessages: ArrayList<QuickMessageModel> = arrayListOf()
    private val _quickMessagesLiveData = MutableLiveData<List<QuickMessageModel>>()
    val quickMessagesLiveData: LiveData<List<QuickMessageModel>>
        get() = _quickMessagesLiveData

    override val typingEventLiveData: LiveData<Event<String?>>
        get() = messageArrivedFlow
            .filter { it.eventType == MqttEventType.TYPING && it.body?.threadId == conversationId && it.body?.userId != myUserId }
            .onEach {
                it.body?.userId?.let { userId ->
                    updateReadMessageFromTypingEventUseCase(
                        conversationId,
                        userId
                    )
                }
            }
            .map { Event(it.body?.user?.name.shortName) }
            .asLiveData()

    override val myUserId: String
        get() = userManager.userId

    private val _conversationFlow: Flow<ConversationModel?> =
        conversationUseCase.getConversationFlowUseCase(conversationId)
            .distinctUntilChanged()
            .flatMapLatest { conversation ->

                // các case chưa có conversation trong realm
                // - trong case người lạ chưa nt bao giờ
                // - nhắn tin rồi nhưng chưa đc lưu trong realm
                flow {
                    val result = conversation ?: when (
                        val getConversation =
                            conversationUseCase.fetchConversationUseCase(conversationId)
                    ) {
                        is Result.Success -> {
                            getConversation.data
                        }

                        is Result.Error -> {
                            null
                        }
                    }

                    if (result?.isSubthread == true && result.parentId != null) {
                        when (
                            val parent =
                                conversationUseCase.fetchConversationUseCase(result.parentId!!)
                        ) {
                            is Result.Success -> {
                                val parentThread = parent.data
                                val subThread = result
                                val canSendMessage = when {
                                    parentThread.isRoleAdminOrOwner || parentThread.isDirect -> true
                                    subThread.isCanSendMessage() == false -> false
                                    parentThread.settings?.disableMemberSendMessage() == false && subThread.settings?.disableMemberSendMessage() == false -> true
                                    parentThread.settings?.disableMemberSendMessage() == true && !parentThread.settings.disableMemberSendSubMessage() && subThread.settings?.disableMemberSendMessage() == false -> true
                                    parentThread.settings?.disableMemberSendMessage() == true && !parentThread.settings.disableMemberSendSubMessage() && subThread.settings?.disableMemberSendMessage() == true -> false
                                    parentThread.settings?.disableMemberSendMessage() == false && !parentThread.settings.disableMemberSendSubMessage() -> false
                                    parentThread.settings?.disableMemberSendMessage() == true && parentThread.settings.disableMemberSendSubMessage() -> false
                                    else -> true
                                }
                                Timber.d("parentThread.settings?.disableMemberSendMessage() == ${parentThread.settings?.disableMemberSendMessage()}")
                                Timber.d("parentThread.settings.disableMemberSendSubMessage() == ${parentThread.settings?.disableMemberSendSubMessage()}")
                                Timber.d("subThread.settings?.disableMemberSendMessage() == ${subThread.settings?.disableMemberSendMessage()}")
                                Timber.d("canSendMessage == $canSendMessage")

                                _canSendMessage.postValue(canSendMessage)
                            }

                            is Result.Error -> {
                                Timber.e(parent.exception)
                            }
                        }
                    } else {
                        _canSendMessage.postValue(result?.isCanSendMessage() == true)
                    }

                    if (result?.isDirect == true && result.partner?.id.orEmpty().isNotEmpty()) {
                        when (
                            val partner =
                                fetchUsersByIdsUseCase(result.partner?.id.orEmpty())
                        ) {
                            is Result.Success -> {
                                emit(
                                    result.copy(
                                        partner = result.partner?.copy(
                                            status = partner.data.status
                                        )
                                    )
                                )
                                _partnerProfileLiveData.postValue(partner.data)
                            }

                            is Result.Error -> {
                                Timber.e(partner.exception)
                                emit(result)
                            }
                        }
                    } else {
                        emit(result)
                    }

                    result?.lastMessage?.let { lastMessage ->
                        if (result.isSubthread) {
                            jumpTo(lastMessage.id)
                        }
                    }
                }
            }
            .flowOn(coroutineDispatchers.default)
            .catch {
                messengerLogger.captureException(it)
                Timber.e(it)
                action(OnFinish)
            }

    override val conversationLiveData: LiveData<ConversationModel?> = _conversationFlow.asLiveData()

    override val messagesPinedLiveData: LiveData<List<MessageModel>> =
        pinedMessageUseCase.getMessagesPinedFlowUseCase(conversationId)
            .distinctUntilChanged()
            .flowOn(coroutineDispatchers.default)
            .asLiveData()

    private val _messagePinedDisplayLiveData = MutableLiveData<MessageModel>()
    override val messagePinedDisplayLiveData: LiveData<MessageModel> = _messagePinedDisplayLiveData

    private val _messagesUiStateFlow = MutableStateFlow(MessengerMessagesUiState())

    val messagesFlow: Flow<PagingData<MessengerViewData>> = combine(
        getMessagesFlowUseCase(conversationId).cachedIn(viewModelScope),
        _messagesUiStateFlow
    ) { pagingData, messagesUiState ->
        pagingData.flatMap { messageModel ->
            messengerMapper.mapMessenger(
                messageModel,
                messagesUiState,
                messageModel.subThreadId > 0
            )
        }
    }
        .distinctUntilChanged()
        .flowOn(coroutineDispatchers.default)
        .catch {
            messengerLogger.captureException(it)
            Timber.e(it)
            action(OnFinish)
        }

    val isConnectedFlow = mqttService.isConnectedFlow
        .distinctUntilChanged()
        .flowOn(coroutineDispatchers.default)
        .catch { throwable ->
            messengerLogger.captureException(throwable)
            Timber.e(throwable)
        }

    val lastMessageFlow: Flow<MessageModel> = getLastMessageFlowUseCase(conversationId)
        .distinctUntilChanged()
        .flowOn(coroutineDispatchers.default)
        .catch {
            messengerLogger.captureException(it)
            Timber.e(it)
            action(OnFinish)
        }

    private val _viewEvent = MutableLiveData<Event<MessengerViewEvent>>()
    val viewEvent: LiveData<Event<MessengerViewEvent>> = _viewEvent

    private val messageArrivedFlow = mqttService.messageArrivedFlow
        .filterStatusForMessenger()
        .map {
            gson.fromJson(it.data.asJsonObject, StatusMqttDto::class.java)
        }
        .flowOn(coroutineDispatchers.default)
        .catch { throwable ->
            messengerLogger.captureException(throwable)
            Timber.e(throwable)
        }

    private val gson = Gson()
    private lateinit var context: Context
    private val messengerMapper: MessengerMapper by lazy {
        MessengerMapperImpl(
            context = context,
            myUser = myUser,
            messengerInteractor = this
        )
    }

    fun setUp(context: Context) {
        this.context = context
    }

    private val myUser: MessengerUserViewData
        get() = MessengerUserViewData(
            myUserId,
            userManager.userProfile?.displayName.orEmpty(),
            userManager.userProfile?.avatar,
            userManager.userProfile?.avatarThumbPattern
        )

    val isGroup by lazy {
        conversationLiveData.value?.isGroup ?: false
    }

    private var setReadMessageJob: Job? = null

    private var setReadMessageBeforeLeaveJob: Job? = null

    private var setAutoDeleteMessageJob: Job? = null

    private val countAutoDeleteMessages = AtomicInteger(0)

    private val queueReadMessage = mutableListOf<Int>()

    init {
        jumpTo(0)
        fetchConversationIfNeed()
        fetchUsersParticipants()
        getDraftMessage()
        updateConversationLastVisit()
        initAutoDeleteMessages()
        enqueueReadMessage()
    }

    override fun onMessengerUnBlockOnClick() {
        viewModelScope.launch {
            userUseCase.blockUserUseCase(conversationLiveData.value?.partner?.id.orEmpty())
        }
    }

    override fun onBlockConversationStrangerOnClick() {
        viewModelScope.launch {
            userUseCase.blockUserUseCase(conversationLiveData.value?.partner?.id.orEmpty())
        }
    }

    override fun onDeleteConversationStrangerOnClick() {
        viewModelScope.launch {
            // TODO neu error se lam gi?
            conversationUseCase.deleteConversationUseCase.invoke(conversationId)
            conversationUseCase.deleteConversationCacheUseCase.invoke(conversationId)
        }
    }

    override fun onAcceptConversationStrangerOnClick() {
        viewModelScope.launch {
            conversationUseCase.moveConversationToFolderUseCase(
                conversationId,
                FolderType.Default.alias
            )
        }
    }

    override fun onMessengerPinSetMessagePinedDisplay(messagePinedDisplay: MessageModel) {
        _messagePinedDisplayLiveData.value = messagePinedDisplay
    }

    override fun onMessengerPinOnClickMessagePinedDisplay(messageId: Int) {
        scrollToMessageId(messageId)
    }

    override fun onMessengerPinOnClickUnPinMessage(messageId: Int) {
        action(OnUnPinMessage(messageId))
    }

    override fun onMessengerPinOnClickSeeMore() {
        action(OnOpenMessagesPinned)
    }

    override fun onMessengerQuickReplySend(option: MessageModel.MessageBodyMetadataOptionModel) {
        val messageRequestModel = MessageRequestModel.buildMessageRequestQuickReplyType(
            conversationId = conversationId,
            text = option.title,
            payload = option.payload
        )
        viewModelScope.launch {
            application.createMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
        }
    }

    override fun onMessengerInputLayoutOnContentChanged(
        data: MessengerInputLayout.Data
    ) {
        _contentLiveData.value = data
    }

    override fun onMessengerInputLayoutOnClickSend() {
        action(OnSendMessage)
    }

    override fun onMessengerInputLayoutOnClickLike() {
        val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
        val messageRequestModel = MessageRequestModel.buildMessageRequestTextType(
            conversationId = conversationId,
            text = application.getString(GapoStrings.messenger_thumb_up),
            replyToMsg = replyMessageId
        )
        createOrEditMessage(messageRequestModel)
    }

    override fun onMessengerInputLayoutOnClickGallery() {
        action(OnOpenGallery)
    }

    override fun onMessengerInputLayoutOnSendSticker(stickerUrl: String) {
        val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
        val messageRequestModel = MessageRequestModel.buildMessageRequestStickerType(
            conversationId = conversationId,
            urlSticker = stickerUrl,
            replyToMsg = replyMessageId
        )
        createOrEditMessage(messageRequestModel, true)
    }

    override fun onMessengerInputLayoutOnClickVoice() {
        action(OnRecordVoice)
    }

    override fun onMessengerInputLayoutOnSendVoice(path: String) {
        createOrEditMessage(
            MessageRequestModel.buildMessageRequestVoiceType(conversationId, path),
            true
        )
    }

    override fun onMessengerInputLayoutOnClickBotCommand() {
        action(
            OnOpenBotCommand(
                threadId = conversationId,
                botId = conversationLiveData.value?.partner?.id.orEmpty()
            )
        )
    }

    override fun onClickedEditMedia(media: Pair<Uri, String>) {
        action(OnOpenSelectedMedia)
    }

    override fun onClickedMedia(media: List<Pair<Uri, String>>, position: Int) {
        viewModelScope.launch(coroutineDispatchers.io) {
            val viewMedias = media.map {
                if (it.first.scheme.isHttp()) {
                    Pair<String, String>(it.first.toString(), it.second)
                } else {
                    Pair<String, String>(UriUtils.uri2FileNoCacheCopy(it.first).path, it.second)
                }
            }
                .map {
                    MediaViewerDeepLink.Media.createSingleMedia(
                        path = it.first,
                        isVideo = it.second.isUriVideo(),
                        quality = if (application.isUploadImageHighQualityEnabled.first()) MessengerConstant.QUALITY_HD else ""
                    )
                }
            viewModelScope.launch(coroutineDispatchers.main) {
                action(OnOpenLocalMedia(viewMedias, position))
            }
        }
    }

    override fun onMessengerInputLayoutOnClickBotList() {
        action(OnOpenBotList(threadId = conversationId))
    }

    override fun onMessengerInputLayoutOnClickBotCommand(botName: String, botId: String) {
        fetchBotCommand(
            threadId = conversationId,
            botId = botId,
            botName = botName
        )
    }

    override fun onAppBarOnClickBack() {
        action(OnFinish)
    }

    override fun onAppBarOnOpenUserProfile(userId: String) {
        action(OnOpenUserProfile(userId))
    }

    override fun onAppBarOnOpenSetting(isGroup: Boolean) {
        action(OnOpenSetting(conversationId.toString(), isGroup))
    }

    override fun onAppBarOnClickAudioCall() {
        action(OnCallAudio())
    }

    override fun onAppBarOnClickVideoCall() {
        action(OnCallVideo())
    }

    override fun onAppBarOnClickMeeting() {
        action(OnCreateMeeting)
    }

    override fun onAppBarOnClickBotList() {
        action(
            OnOpenBotList(
                threadId = conversationId,
                botId = conversationLiveData.value?.partner?.id.orEmpty()
            )
        )
    }

    override fun onAppBarOnClickCancelSelection() {
        _selectedMessageLiveData.value = emptyList()
    }

    override suspend fun isMember(userId: String): Boolean {
        return when (
            val result =
                userUseCase.getUsersParticipantsByIdUseCase(conversationId, userId)
        ) {
            is Result.Success -> {
                result.data.role == ConversationRole.MEMBER
            }

            else -> true
        }
    }

    override fun onSelectedMessageActionOnClickDelete() {
        viewModelScope.launch {
            val messagesSelected = selectedMessageLiveData.value
            messagesSelected?.let {
                val conversation = conversationLiveData.value ?: return@launch
                val myUserId = myUserId
                val messagesSelectedCanDelete = messagesSelected.filter {
                    it.canDelete(
                        conversation,
                        myUserId,
                        isMember(it.senderId)
                    )
                }
                action(OnMessageDeleteAction(messagesSelectedCanDelete))
            }
        }
    }

    override fun onSelectedMessageActionOnClickForward(messageIds: List<Int>) {
        action(OnMessageForwardAction(messageIds))
    }

    override fun onSelectedMessageActionOnClickSave(messageIds: List<Int>) {
        saveMessages(messageIds)
    }

    override fun onSelectedMessageActionOnCancelSelection() {
        onAppBarOnClickCancelSelection()
    }

    override fun onMessengerActionChatOnClickPoll() {
        action(OnCreatePoll)
    }

    override fun onMessengerActionChatOnClickCamera() {
        action(OnOpenCamera)
    }

    override fun onMessengerActionChatOnClickGallery() {
        action(OnOpenGallery)
    }

    override fun onMessengerActionChatOnClickAttachment() {
        action(OnAttachFile)
    }

    override fun onMessageActionOnReact(messageId: Int, reactType: MessengerReactType) {
        viewModelScope.launch {
            reactMessageUseCase.invoke(conversationId, messageId, reactType.type)
        }
    }

    override fun onMessageActionOnReply(messageReplied: MessageModel) {
        reply(messageReplied)
    }

    override fun onMessageActionOnForward(messageId: Int) {
        action(OnMessageForwardAction(listOf(messageId)))
    }

    override fun onMessageActionOnSave(messageId: Int) {
        saveMessages(listOf(messageId))
    }

    override fun onMessageActionOnDelete(messageDeleted: MessageModel) {
        action(OnMessageDeleteAction(listOf(messageDeleted)))
    }

    override fun onMessageActionOnEdit(messageEdit: MessageModel) {
        action(OnShowKeyboard)
        onMessengerInputLayoutOnContentChanged(
            messageEdit.mapToMessageInputLayoutData()
        )
    }

    override fun onMessageActionOnMultipleChoice(message: MessageModel) {
        onMessengerMessageOnSelectMessage(message)
    }

    override fun onMessageActionOnPin(messageId: Int) {
        viewModelScope.launch {
            when (pinedMessageUseCase.pinMessageUseCase(conversationId, messageId)) {
                is Result.Success -> {
                    action(OnShowPositiveSnackBar(application.getString(GapoStrings.messenger_pin_alert_pin_message_success)))
                }

                is Result.Error -> {
                    action(OnShowNegativeSnackBar(application.getString(GapoStrings.messenger_pin_alert_pin_message_failure)))
                }
            }
        }
    }

    override fun onMessageActionOnUnPin(messageId: Int) {
        viewModelScope.launch {
            when (pinedMessageUseCase.unPinMessageUseCase(conversationId, messageId)) {
                is Result.Success -> {
                    action(OnShowPositiveSnackBar(application.getString(GapoStrings.messenger_pin_alert_un_pin_message_success)))
                }

                is Result.Error -> {
                    action(OnShowNegativeSnackBar(application.getString(GapoStrings.messenger_pin_alert_un_pin_message_failure)))
                }
            }
        }
    }

    override fun onMessageActionOnCreateTask(
        conversation: ConversationModel,
        message: MessageModel
    ) {
        action(OnMessageCreateTask(conversation, message))
    }

    override fun onMessageActionOnReplyInThread(subthreadId: Long?, messageId: Int) {
        if (subthreadId != null) {
            action(OnOpenSubthread(subthreadId))
        } else {
            action(OnOpenSubthread(conversationId, messageId))
        }
    }

    override fun onMessageActionOnMarkUnread(messageId: Int) {
        viewModelScope.launch {
            when (val result = markUnreadMessageUseCase(conversationId, messageId)) {
                is Result.Success -> {
                    action(OnFinish)
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    action(
                        OnShowNegativeSnackBar(
                            result.exception.message.orEmpty()
                                .ifEmpty { application.getString(GapoStrings.shared_error_general) }
                        )
                    )
                }
            }
        }
    }

    override fun onMessageActionOnGoToOriginalMessage(threadId: String, messageId: Int) {
        viewModelScope.launch {
            when (val result = conversationUseCase.fetchConversationUseCase(threadId.toLong())) {
                is Result.Success -> {
                    when (threadId) {
                        conversationId.toString() -> action(OnScrollToMessageId(messageId))
                        else -> action(
                            MessengerViewEvent.OnGoToOriginalMessage(
                                threadId,
                                messageId,
                                result.data.isSubthread
                            )
                        )
                    }
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    action(
                        OnShowNegativeSnackBar(
                            result.exception.message.orEmpty()
                                .ifEmpty { application.getString(GapoStrings.shared_error_general) }
                        )
                    )
                }
            }
        }
    }

    override fun onAvatarActionOpenUserProfile(userId: String) {
        action(OnOpenUserProfile(userId))
    }

    override fun onAvatarActionAudioCall(userId: String) {
        viewModelScope.launch {
            userForCall = getUsersParticipantsByIdUseCaseForCalling(userId)
            action(OnCallAudio(userId))
        }
    }

    override fun onAvatarActionVideoCall(userId: String) {
        viewModelScope.launch {
            userForCall = getUsersParticipantsByIdUseCaseForCalling(userId)
            action(OnCallVideo(userId))
        }
    }

    override fun onAvatarActionOpenChatDetailWithUserId(userId: String) {
        action(OnOpenConversation(userId = userId))
    }

    override fun onAvatarActionAddMentionToInput(userId: String, userName: String) {
        val content = contentLiveData.value ?: MessengerInputLayout.Data()
        onMessengerInputLayoutOnContentChanged(content.addMention(userId, userName))
        action(OnShowKeyboard)
    }

    override fun onMessengerMessageOnLongClickMessage(messageId: Int) {
        if (conversationLiveData.value?.isRootMessageOfSubthread(messageId) == true) {
            return
        }
        action(OnOpenMessageAction(messageId))
    }

    override fun onMessengerMessageOnDoubleClickMessage(messageId: Int) {
        if (conversationLiveData.value?.isRootMessageOfSubthread(messageId) == true) {
            return
        }
        onMessageActionOnReact(messageId, MessengerReactType.FIRST)
    }

    override fun onMessengerMessageOnClickUserSeen(
        totalMember: Int,
        totalSeen: List<MessengerUserViewData>
    ) {
        action(OnOpenUserSeen(totalMember, totalSeen))
    }

    override fun onMessengerMessageOnClickUserAvatar(userId: String) {
        action(OnOpenAvatarUserAction(userId))
    }

    override fun onMessengerMessageOnSelectMessage(message: MessageModel) {
        val selected = selectedMessageLiveData.value.orEmpty().toMutableList()
        selected.add(message)
        _selectedMessageLiveData.value = selected
    }

    override fun onMessengerMessageOnUnselectMessage(message: MessageModel) {
        val selected = selectedMessageLiveData.value?.toMutableList() ?: return
        selected.removeAll { it.id == message.id }
        _selectedMessageLiveData.value = selected
    }

    override fun onMessengerMessageOnClickErrorMessage(message: MessageModel) {
        action(OnClickErrorMessage(message))
    }

    override fun onMessengerMessageOnClickLink(link: String) {
        action(OnOpenLink(link))
    }

    override fun onMessengerMessageOnToggleExpandTimeContent(messageId: Int) {
        val state = _messagesUiStateFlow.value
        _messagesUiStateFlow.value = state.toggleExpandTime(messageId)
    }

    override fun onMessengerMessageOnClickEmail(email: String) {
        action(OnOpenEmail(email))
    }

    override fun onMessengerMessageOnClickPhoneNumber(phoneNumber: String) {
        action(OnOpenPhone(phoneNumber))
    }

    override fun onMessengerMessageDynamicOnClickedButton(
        message: MessageModel?,
        payload: String,
        text: String,
        openInApp: Boolean
    ) {
        if (message.toString().contains(
                MessageCarouselButtonType.POSTBACK.type
            )
        ) {
            if (isGroup && message?.sender?.isBot() == true && message.toString().contains(
                    MessageCarouselButtonType.POSTBACK.type
                )
            ) {
                action(
                    MessengerViewEvent.OnAddDynamicMessageBotCommand(
                        botId = message.sender.id,
                        botName = message.sender.name,
                        botCommand = MessageBotCommandModel(
                            command = payload,
                            description = text,
                            response = ""
                        )
                    )
                )
            } else {
                onMessengerMessageOnSendQuickReply(title = text, payload = payload)
            }
        } else {
            onMessengerOpenLink(
                botId = message?.sender?.id.toString(),
                payload = payload,
                openInApp = openInApp
            )
        }
    }

    override fun onMessengerMessageOnClickReplyMessage(messageId: Int) {
        scrollToMessageId(messageId)
    }

    override fun onMessengerMessageOnClickMedia(id: String, thumb: String?) {
        action(OnOpenMedia(id, thumb))
    }

    override fun onMessengerMessageOnSendQuickReply(title: String, payload: String) {
        val messageRequestModel = MessageRequestModel.buildMessageRequestQuickReplyType(
            conversationId = conversationId,
            text = title,
            payload = payload
        )
        createOrEditMessage(messageRequestModel)
    }

    override fun onMessengerMessageOnSendPostBack(
        botId: String,
        botName: String,
        title: String,
        payload: String
    ) {
        if (isGroup) {
            action(
                MessengerViewEvent.OnAddDynamicMessageBotCommand(
                    botId = botId,
                    botName = botName,
                    botCommand = MessageBotCommandModel(
                        command = payload,
                        description = title,
                        response = ""
                    )
                )
            )
        } else {
            onMessengerMessageOnSendQuickReply(title = title, payload = payload)
        }
    }

    override fun onMessengerMessageOnLockSlidr(lock: Boolean) {
        if (lock != lockSlidrEventLiveData.value?.peek()) {
            _lockSlidrEventLiveData.value = Event(lock)
        }
    }

    override fun onMessengerOpenLink(botId: String, payload: String, openInApp: Boolean) {
        when {
            GapoWorkLink.startsWithGWUrl(payload) || GapoWorkLink.startsWithCustomUrl(payload) -> {
                context.openWebBrowser(payload)
            }

            botId.isNotEmpty() -> {
                viewModelScope.launch {
                    when (
                        val result = getSignedRequest(
                            threadId = conversationId.toString(),
                            botId = botId
                        )
                    ) {
                        is Result.Success -> {
                            val baseUrl = payload.urlDecode()
                            val extendParam = "signed_request=${result.data}"
                            val urlWithParams = if (baseUrl.contains("?")) {
                                "$baseUrl&$extendParam"
                            } else {
                                "$baseUrl?$extendParam"
                            }

                            if (openInApp) {
                                context.startPreviewBrowser(
                                    urlWithParams
                                )
                            } else {
                                context.openWebBrowser(urlWithParams)
                            }
                        }

                        is Result.Error -> {
                            if (openInApp) {
                                context.startPreviewBrowser(payload)
                            } else {
                                context.openWebBrowser(payload)
                            }
                        }
                    }
                }
            }

            !openInApp -> {
                context.openWebBrowser(payload)
            }

            else -> {
                context.startPreviewBrowser(payload)
            }
        }
    }

    override fun onMessengerMediaOnLockSlider(lock: Boolean) {
        if (lock != lockSlidrEventLiveData.value?.peek()) {
            _lockSlidrEventLiveData.value = Event(lock)
        }
    }

    override fun onMessengerMessageOnDownloadFile(url: String, name: String) {
        action(OnDownloadFile(url, name))
    }

    override fun onMessengerMessageOnVote(messageId: Int, voteId: String) {
        viewModelScope.launch {
            chooseVoteUseCase(conversationId, messageId, voteId, true)
        }
    }

    override fun onMessengerMessageOnUnVote(messageId: Int, voteId: String) {
        viewModelScope.launch {
            chooseVoteUseCase(conversationId, messageId, voteId, false)
        }
    }

    override fun onMessengerMessageOnShowVotedUser(messageId: Int, voteId: String, voteCount: Int) {
        action(OnShowVotedUser(messageId, voteId, voteCount))
    }

    override fun onMessengerMessageAddVote(messageId: Int) {
        action(OnAddVote(messageId))
    }

    override fun onMessengerMessageOnToggleExpandPoll(messageId: Int) {
        _messagesUiStateFlow.value = _messagesUiStateFlow.value.toggleExpandPoll(messageId)
    }

    override fun onMessengerMessageOnClickPlayVoice(url: String) {
        action(OnPlayVoice(url))
    }

    override fun onMessengerMessageOnClickPauseVoice() {
        action(OnPauseVoice)
    }

    override fun onMessengerMessageOnClickReaction(messageId: Int) {
        action(OnOpenUserReaction(messageId))
    }

    override fun onMessengerMessageOnAudioCall() {
        action(OnCallAudio())
    }

    override fun onMessengerMessageOnVideoCall() {
        action(OnCallVideo())
    }

    override fun onMessengerMessageOnGroupCall(roomId: String) {
        joinGroupCallUseCase(roomId)
    }

    override fun onMessengerMessageOnDownloadMeetingFile(
        fileRecordId: String,
        fileRecordName: String
    ) {
        val url = "$meetingFileUrl$fileRecordId"
        action(OnDownloadFile(url, fileRecordName))
    }

    override fun onMessengerMessageOnCancelDownloadMeetingFile(fileRecordId: String) {
        val url = "$meetingFileUrl$fileRecordId"
        application.cancelDownloadFile(url)
    }

    override fun onMessengerMessageOnWatchMeetingFile(fileRecordId: String) {
        val url = "$meetingFileUrl$fileRecordId"
        action(OnWatchMeetingFile(url))
    }

    override fun onMessengerMessageOnOpenSubthread(subthreadId: Long) {
        action(OnOpenSubthread(subthreadId))
    }

    override fun onMessengerMessageOnClickMention(mentionId: String) {
        if (mentionId == MessengerMentionUserViewData.MENTION_ALL_ID) return
        action(OnOpenMentionUserAction(mentionId))
    }

    override fun onMessengerSeenListOnClickUserSeen(
        totalMember: Int,
        totalSeen: List<MessengerUserViewData>
    ) {
        action(OnOpenUserSeen(totalMember, totalSeen))
    }

    override fun onMessengerCombineActionNoteClick(messageModel: MessageModel) {
        action(
            MessengerViewEvent.OnOpenCombineActionNote(
                messageModel
            )
        )
    }

    override fun onMessengerActionNoteClick(messageModel: MessageModel) {
        if ((messageModel.body.metadata?.actionNoteDataChanged?.pinnedMessageId ?: 0) > 0) {
            scrollToMessageId(
                messageModel.body.metadata?.actionNoteDataChanged?.pinnedMessageId ?: 0
            )
        } else {
            action(
                MessengerViewEvent.OnOpenActionNote(
                    messageModel
                )
            )
        }
    }

    fun jumpTo(messageId: Int) {
        Timber.e("akaizz scroll jumpTo $messageId")
        viewModelScope.launch {
            getMessagesFlowUseCase.jumpTo(messageId)
        }
    }

    fun connectedMessageMqtt(conversationId: Long) {
        viewModelScope.launch {
            connectedMessageMqttUseCase.invoke(conversationId)
        }
    }

    fun sendImageFromCamera() {
        viewModelScope.launch {
            application.setUploadHighQualityEnabled(true)
        }
        val imagePath = imagePathFromCamera ?: return
        val messageRequestModel = MessageRequestModel.buildMessageRequestImageType(
            conversationId = conversationId,
            pathsImage = listOf(imagePath),
            isQualityHD = true
        )
        createOrEditMessage(messageRequestModel)
    }

    fun deleteMessages(messageIds: List<Int>, conversationId: Long, level: MessageLevelDeleteType) {
        viewModelScope.launch {
            val result = deleteMessagesUseCase(messageIds, conversationId, level)
            if (result is Result.Error) {
                action(OnShowNegativeSnackBar(application.getString(GapoStrings.message_alert_remove_message_failure)))
            } else {
                _selectedMessageLiveData.value = emptyList()
            }
        }
    }

    fun sendMediaFromGallery(
        quickMessage: QuickMessageModel,
        media: List<Uri>,
        isSendHd: Boolean
    ) {
        val text = contentLiveData.value?.getNormalizedContent().orEmpty()
        val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
        viewModelScope.launch(coroutineDispatchers.io) {
            application.setUploadHighQualityEnabled(isSendHd)

            // Handle from Local Images
            val mediaFile = try {
                media.filter { !it.scheme.isHttp() }.mapNotNull {
                    UriUtils.uri2FileNoCacheCopy(it)
                }
            } catch (e: Exception) {
                Timber.e(e)
                emptyList()
            }

            val images: List<String> = mediaFile.mapNotNull {
                if (it.path.isVideo()) {
                    null
                } else {
                    it.path
                }
            }

            val imagesFromQuickMessage: List<String> = media.filter {
                it.scheme.isHttp()
            }.map { it.toString() }

            val messageRequestModel =
                MessageRequestModel.buildMessageRequestMultiImageTypeByQuickMessages(
                    conversationId = conversationId,
                    quickMessage = quickMessage,
                    imagesFromQuickMessage = imagesFromQuickMessage,
                    text = text,
                    pathsImage = images,
                    replyToMsg = replyMessageId,
                    mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                        .mapToMessageMentionRequestModel()
                )
            application.createMessageWithQuickMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
            withContext(coroutineDispatchers.main) {
                onMessengerInputLayoutOnContentChanged(MessengerInputLayout.Data())
            }
            scrollToBottomDelaySecond()
        }
    }

    fun sendMediaFromGallery(media: List<Uri>, isSendHd: Boolean) {
        val text = contentLiveData.value?.getNormalizedContent().orEmpty()
        val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
        viewModelScope.launch(coroutineDispatchers.io) {
            application.setUploadHighQualityEnabled(isSendHd)
            val mediaFile = try {
                media.filter { !it.scheme.isHttp() }.mapNotNull {
                    UriUtils.uri2FileNoCacheCopy(it)
                }
            } catch (e: Exception) {
                Timber.e(e)
                emptyList()
            }
            launch(coroutineDispatchers.main) {
                val images: List<String> = mediaFile.mapNotNull {
                    if (it.path.isVideo()) {
                        val messageRequestModel = MessageRequestModel.buildMessageRequestVideoType(
                            conversationId = conversationId,
                            pathVideo = it.path,
                            text = if (mediaFile.size == 1) text else "",
                            replyToMsg = replyMessageId,
                            mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                                .mapToMessageMentionRequestModel()
                        )
                        createOrEditMessage(messageRequestModel, mediaFile.size > 1)
                        null
                    } else {
                        it.path
                    }
                }
                if (images.isNotEmpty()) {
                    val messageRequestModel = MessageRequestModel.buildMessageRequestImageType(
                        conversationId = conversationId,
                        pathsImage = images,
                        text = text,
                        isQualityHD = isSendHd,
                        replyToMsg = replyMessageId,
                        mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                            .mapToMessageMentionRequestModel()
                    )
                    createOrEditMessage(messageRequestModel)
                }
            }
        }
    }

    fun sendFiles(uris: List<Uri>) {
        uris.forEach { uri ->
            val fileInfo = application.getFileNameAndSizeByUri(uri)
            val name = fileInfo.first
            val size = fileInfo.second
            val messageError = when {
                name.isUnSupportedFileExt() -> application.getString(GapoStrings.common_file_error_un_support)
                    .replaceByBrandingName(brandName)

                size != -1L && size > MessengerConstant.MAX_FILE_SIZE -> application.getString(
                    GapoStrings.common_file_error_max_length
                )

                else -> null
            }
            if (messageError != null) {
                action(OnWarning(messageError))
            } else {
                val text = contentLiveData.value?.getNormalizedContent().orEmpty()
                val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
                val messageRequestModel = MessageRequestModel.buildMessageRequestFileType(
                    conversationId = conversationId,
                    uriFile = uri.toString(),
                    text = text,
                    replyToMsg = replyMessageId,
                    name = name,
                    size = size,
                    mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                        .mapToMessageMentionRequestModel()
                )
                createOrEditMessage(messageRequestModel)
            }
        }
    }

    fun sendPoll(pollVoteCreatorData: PollVoteCreatorViewData) {
        createOrEditMessage(
            MessageRequestModel.buildMessageRequestPollType(
                conversationId = conversationId,
                pollInformation = pollVoteCreatorData.mapToRequest()
            )
        )
    }

    fun reply(message: MessageModel) {
        val content = contentLiveData.value ?: MessengerInputLayout.Data()
        val previewData = content.previewData.copyOrCreate(replyMessage = message)
        onMessengerInputLayoutOnContentChanged(
            content.copy(previewData = previewData)
        )
        action(OnShowKeyboard)
    }

    fun addVote(messageId: String, voteTitle: String) {
        val messageIdInt = messageId.toIntOrNull() ?: return
        viewModelScope.launch {
            val result = createVoteUseCase(conversationId, messageIdInt, voteTitle)
            if (result is Result.Error) {
                action(OnShowNegativeSnackBar(application.getString(GapoStrings.shared_error_general)))
            }
        }
    }

    fun scrollToMessageId(messageId: Int) {
        Timber.e("akaizz scroll ToMessageId $messageId")
        action(OnScrollToMessageId(messageId))
    }

    private fun scrollToBottomDelaySecond(delayInSecond: Long = 1000) {
        viewModelScope.launch(coroutineDispatchers.io) {
            Timber.e("akaizz scroll ToBottomDelaySecond")
            delay(delayInSecond)
            launch(coroutineDispatchers.main) {
                action(MessengerViewEvent.OnScrollToBottomDelaySecond)
            }
        }
    }

    fun scrollToBottom() {
        val lastMessageId = conversationLiveData.value?.lastMessage?.id ?: return
        Timber.e("akaizz scroll ToBottom $lastMessageId")
        action(OnScrollToBottom(lastMessageId))
    }

    fun setReadMessage(messageId: Int) {
        val conversation = conversationLiveData.value ?: return
        val lastMessageId = conversation.lastMessage?.id ?: return
        val lastReadMessageId = lastMessageId - conversation.unReadCount

        if (messageId > lastReadMessageId) {
            queueReadMessage.clear()
            queueReadMessage.add(messageId)
        }
    }

    fun setReadMessageForBackPressed(messageId: Int) {
        val conversation = conversationLiveData.value ?: return
        val lastMessageId = conversation.lastMessage?.id ?: return
        val lastReadMessageId = lastMessageId - conversation.unReadCount

        if (messageId > lastReadMessageId) {
            setReadMessageBeforeLeaveJob?.cancel()
            setReadMessageBeforeLeaveJob = viewModelScope.launch {
                readMessageUseCase(conversationId, messageId)
            }
        }
    }

    private fun enqueueReadMessage() {
        setReadMessageJob = viewModelScope.launch {
            while (isActive) {
                if (queueReadMessage.isNotEmpty()) {
                    readMessageUseCase(conversationId, queueReadMessage.first())
                    queueReadMessage.clear()
                }
                delay(300)
            }
        }
    }

    fun deleteErrorMessage(messageCreatedAt: Long) {
        viewModelScope.launch {
            deleteMessageErrorUseCase(conversationId, messageCreatedAt)
        }
    }

    fun retrySendErrorMessage(messageCreatedAt: Long) {
        viewModelScope.launch {
            val result = getMessageRequestErrorUseCase(conversationId, messageCreatedAt)
            if (result is Result.Success) {
                val errorMessageRequestModel: MessageRequestModel = result.data
                if (errorMessageRequestModel.body?.messageImages?.firstOrNull()?.url.isHttp() == true) {
                    application.createMessageWithQuickMessage(
                        errorMessageRequestModel,
                        saveMessageCreatorRequestUseCase,
                        coroutineDispatchers
                    )
                } else {
                    application.createMessage(
                        errorMessageRequestModel,
                        saveMessageCreatorRequestUseCase,
                        coroutineDispatchers
                    )
                }
            }
        }
    }

    fun onMessengerBotCommandSend(message: MessageBotCommandModel) {
        val messageRequestModel = MessageRequestModel.buildMessageRequestMenuBotCommandType(
            conversationId = conversationId,
            text = message.description,
            payload = message.command
        )
        viewModelScope.launch {
            application.createMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
        }
    }

    fun onMessengerBotListCommandSend(message: MessageBotCommandModel) {
        val messageRequestModel = MessageRequestModel.buildMessageRequestMenuBotCommandType(
            conversationId = conversationId,
            text = "${
            contentLiveData.value?.getNormalizedContent().orEmpty()
            } ${message.description}",
            payload = message.command,
            mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                .mapToMessageMentionRequestModel()
        )
        viewModelScope.launch {
            application.createMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
        }
        action(OnClearedInputMessageLayout)
    }

    fun onMessengerDynamicMessageSend(message: MessageBotCommandModel) {
        val messageRequestModel = MessageRequestModel.buildMessageRequestMenuBotCommandType(
            conversationId = conversationId,
            text = "${
            contentLiveData.value?.getNormalizedContent().orEmpty()
            } ${message.description}",
            payload = message.command,
            mentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
                .mapToMessageMentionRequestModel(),
            type = MessageBodyType.QUICK_REPLY
        )
        viewModelScope.launch {
            application.createMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
        }
        action(OnClearedInputMessageLayout)
    }

    fun setKeySearch(keySearch: String?) {
        _messagesUiStateFlow.value = _messagesUiStateFlow.value.setHighlightText(keySearch)
    }

    private fun getDraftMessage() {
        viewModelScope.launch {
            val result =
                conversationUseCase.getConversationByIdUseCase(conversationId) as? Result.Success
                    ?: return@launch
            val draftMessage = result.data.messageDraft
            draftMessage?.mapToMessengerInputLayoutData()
                ?.let {
                    onMessengerInputLayoutOnContentChanged(it)
                }
        }
    }

    private fun updateConversationLastVisit() {
        viewModelScope.launch {
            updateLastVisitAtConversationUseCase(conversationId, System.currentTimeMillis())
        }
    }

    /**
     * biến canCall trong direct conversation khi vào detail phải đc lấy từ remote
     */
    private fun fetchConversationIfNeed() {
        viewModelScope.launch {
            when (val result = conversationUseCase.getConversationByIdUseCase(conversationId)) {
                is Result.Success -> {
                    val conversation = result.data
                    // update markUnread
                    if (conversation.markUnread) {
                        updateMarkUnReadConversationUseCase(conversationId, false)
                    }
                    if (conversation.type == ConversationType.DIRECT && !conversation.canCall()) {
                        conversationUseCase.fetchConversationUseCase(conversationId)
                    }
                }

                else -> {}
            }
        }
    }

    private fun fetchUsersParticipants() {
        viewModelScope.launch {
            when (val result = userUseCase.fetchUsersParticipantsUseCase(conversationId)) {
                is Result.Error -> {
                    Timber.e(result.exception)
                }

                is Result.Success -> {
                    _participantsLiveData.postValue(result.data)
                }
            }
        }
    }

    private fun createOrEditMessage(
        messageRequestModel: MessageRequestModel,
        keepContent: Boolean = false
    ) {
        if (!keepContent) {
            onMessengerInputLayoutOnContentChanged(MessengerInputLayout.Data())
        }
        viewModelScope.launch {
            if (messageRequestModel.isEdit) {
                application.editMessage(
                    messageRequestModel,
                    editMessageCacheActionUseCase,
                    coroutineDispatchers
                )
                scrollToBottomDelaySecond()
            } else {
                application.createMessage(
                    messageRequestModel,
                    saveMessageCreatorRequestUseCase,
                    coroutineDispatchers
                )
                scrollToBottom()
            }
        }
    }

    private fun createMultipleMessages(
        messageRequestModels: List<MessageRequestModel>,
        keepContent: Boolean = false
    ) {
        if (!keepContent) {
            onMessengerInputLayoutOnContentChanged(MessengerInputLayout.Data())
        }
        viewModelScope.launch {
            messageRequestModels.forEach { messageRequestModel ->
                runBlocking {
                    application.createMessage(
                        messageRequestModel,
                        saveMessageCreatorRequestUseCase,
                        coroutineDispatchers
                    )
                }
            }
            scrollToBottom()
        }
    }

    private suspend fun getMessageRequestModels(): List<MessageRequestModel> {
        val resultRequest = LinkedList<MessageRequestModel>()
        val text = contentLiveData.value?.getNormalizedContent().orEmpty()
        val normalizedMentions = contentLiveData.value?.getNormalizedMentions().orEmpty()
        val mentions = normalizedMentions.mapToMessageMentionRequestModel()
        val previewLink =
            contentLiveData.value?.previewData?.previewLink?.mapToMessagePreviewLinkRequestModel()
        val editMessageId = contentLiveData.value?.previewData?.editMessage?.id ?: Int.MAX_VALUE
        val replyMessageId = contentLiveData.value?.previewData?.replyMessage?.id
        val payload = if (mentions.size == 1 && mentions.any { it.target.orEmpty().isBot() }) {
            val mentionsText = normalizedMentions.first().mentionText
            val commands: List<MessageBotCommandModel> = when (
                val result =
                    fetchBotCommandsUseCase(mentions.firstOrNull()?.target.orEmpty())
            ) {
                is Result.Success -> {
                    result.data
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    emptyList()
                }
            }
            if (commands.isNotEmpty()) {
                extractBotPayload(text, mentionsText.trim(), commands.map { it.description.trim() })
            } else ""
        } else ""
        if (text.length > MAX_INPUT_MESSAGE && replyMessageId == null && contentLiveData.value?.previewData?.editMessage?.id == null &&
            contentLiveData.value?.previewData?.previewLink == null
        ) {
            MessengerUtils.splitMessagesBySubstring(text, normalizedMentions)
                .forEachIndexed { index, pairSplitTextMention ->
                    val createdAt = System.currentTimeMillis() + index
                    resultRequest.add(
                        MessageRequestModel(
                            threadId = conversationId,
                            id = editMessageId,
                            clientId = createdAt,
                            createdAt = createdAt,
                            body = MessageRequestModel.MessageBodyRequestModel(
                                text = pairSplitTextMention.first,
                                type = MessageBodyType.TEXT,
                                metadata = MessageRequestModel.MessageMetadataRequestModel(
                                    previewLink = previewLink,
                                    mentions = pairSplitTextMention.second.mapToMessageMentionRequestModel(),
                                    payload = payload
                                ),
                                isMarkdownText = false,
                                replyToMsg = null
                            )
                        )
                    )
                }
        } else {
            resultRequest.add(
                MessageRequestModel(
                    threadId = conversationId,
                    id = editMessageId,
                    clientId = System.currentTimeMillis(),
                    body = MessageRequestModel.MessageBodyRequestModel(
                        text = text,
                        type = MessageBodyType.TEXT,
                        metadata = MessageRequestModel.MessageMetadataRequestModel(
                            previewLink = previewLink,
                            mentions = mentions,
                            payload = payload
                        ),
                        isMarkdownText = false,
                        replyToMsg = replyMessageId
                    )
                )
            )
        }
        return resultRequest
    }

    private fun extractBotPayload(
        text: String,
        botName: String,
        botCommands: List<String>
    ): String {
        var result = ""
        botCommands.forEach { command ->
            val suffix = arrayListOf("$botName$command", "$botName $command")
            val prefix = arrayListOf("$command$botName", "$command $botName")
            if (text.contains(suffix.first(), true) || text.contains(suffix.last(), true)) {
                result = command
                return@forEach
            }
            if (text.contains(prefix.first(), true) || text.contains(prefix.last(), true)) {
                result = command
                return@forEach
            }
        }
        return result
    }

    private fun action(event: MessengerViewEvent) {
        _viewEvent.value = Event(event)
    }

    private fun saveMessages(messageIds: List<Int>) {
        viewModelScope.launch {
            when (sendMessagesToSaveUseCase(conversationId, messageIds)) {
                is Result.Success -> {
                    action(OnShowPositiveSnackBar(application.getString(GapoStrings.messenger_save_message_successful)))
                }

                is Result.Error -> {
                    action(OnShowNegativeSnackBar(application.getString(GapoStrings.shared_error_general)))
                }
            }
        }
    }

    override fun fetchCollab(collabId: String) {
        viewModelScope.launch {
            when (val result = fetchCollabByIdUseCase(collabId)) {
                is Result.Success -> {
                    _collabLiveData.value = result.data
                }

                else -> {}
            }
        }
    }

    fun setParamsMessageCreateTask(
        conversation: ConversationModel,
        message: MessageModel,
        mentions: List<MessageUserModel>
    ) {
        val bundle = bundleOf(
            MiniTaskCreatorFromMessengerDeepLink.INPUT_EXTRA to MiniTaskCreatorFromMessengerDeepLink.Input
                .Builder()
                .setCollabId(conversation.collabId)
                .setDescription(message.body.text)
                .setCreator(
                    MiniTaskCreatorFromMessengerDeepLink.Input.User(
                        userManager.userId,
                        userManager.userProfile?.displayName.orEmpty(),
                        userManager.userProfile?.avatar.orEmpty(),
                        userManager.userProfile?.avatarThumbPattern.orEmpty()
                    )
                )
                .setAssignees(
                    mentions.map { mention ->
                        MiniTaskCreatorFromMessengerDeepLink.Input.User(
                            mention.userId,
                            mention.displayName,
                            mention.avatar,
                            mention.avatarThumbPattern
                        )
                    }
                )
                .setWatchers(
                    listOf(
                        MiniTaskCreatorFromMessengerDeepLink.Input.User(
                            userManager.userId,
                            userManager.userProfile?.displayName.orEmpty(),
                            userManager.userProfile?.avatar.orEmpty(),
                            userManager.userProfile?.avatarThumbPattern.orEmpty()
                        )
                    )
                )
                .setAttachmentFiles(
                    buildList {
                        addAll(
                            message.body.messageImages.map { image ->
                                MiniTaskCreatorFromMessengerDeepLink.Input.Media.Image(
                                    MessageBodyType.IMAGE.type,
                                    image.id,
                                    image.media,
                                    image.media.substringAfterLast("/"),
                                    image.size,
                                    image.width,
                                    image.height,
                                    GapoThumbPattern.AVATAR_MEDIUM_SIZE.parse(
                                        image.media,
                                        image.url
                                    )
                                )
                            }
                        )

                        addAll(
                            message.body.messageVideos.map { video ->
                                MiniTaskCreatorFromMessengerDeepLink.Input.Media.Video(
                                    MessageBodyType.VIDEO.type,
                                    video.id,
                                    video.url,
                                    video.name,
                                    video.size,
                                    video.width,
                                    video.height,
                                    video.thumb,
                                    video.type.type
                                )
                            }
                        )

                        addAll(
                            message.body.messageFiles.map { file ->
                                MiniTaskCreatorFromMessengerDeepLink.Input.Media.File(
                                    MessageBodyType.FILE.type,
                                    file.id,
                                    file.fileUrl,
                                    file.fileName,
                                    file.fileSize,
                                    file.type
                                )
                            }
                        )
                    }
                ).build()
        )
        _inputCreateTaskLiveData.value = bundle
    }

    fun getMentionsParamsMessageCreateTask(
        conversation: ConversationModel,
        message: MessageModel,
        userIds: List<String>
    ) {
        viewModelScope.launch {
            when (val result = userUseCase.getMessageUserUseCase(userIds)) {
                is Result.Success -> {
                    setParamsMessageCreateTask(conversation, message, result.data)
                }

                else -> {}
            }
        }
    }

    fun checkExistAutoDeleteMessage(messageModel: MessageModel) {
        viewModelScope.launch(coroutineDispatchers.io) {
            if (messageModel.willDeletedAt > 0) {
                countAutoDeleteMessages.incrementAndGet()
                deleteAutoDeleteMessage()
            }
        }
    }

    fun isAutoDeleteThread(): Boolean {
        return (conversationLiveData.value?.settings?.deleteMsgAfterDays ?: 0) > 0
    }

    private fun deleteAutoDeleteMessage() {
        setAutoDeleteMessageJob?.cancel()
        setAutoDeleteMessageJob = viewModelScope.launch(coroutineDispatchers.io) {
            while (isActive && countAutoDeleteMessages.get() > 0) {
                when (
                    val deletedMessageCountResult =
                        autoDeleteMessageUseCase.invoke(conversationId)
                ) {
                    is Result.Success -> {
                        countAutoDeleteMessages.set(countAutoDeleteMessages.get() - deletedMessageCountResult.data)
                        if (countAutoDeleteMessages.get() <= 0) {
                            setAutoDeleteMessageJob?.cancel()
                        } else {
                            delay(1000)
                        }
                    }

                    is Result.Error -> {
                        Timber.e(deletedMessageCountResult.exception)
                    }
                }
            }
        }
    }

    private fun initAutoDeleteMessages() {
        viewModelScope.launch(coroutineDispatchers.io) {
            when (val result = countAutoDeleteMessageUseCase.invoke(conversationId)) {
                is Result.Success -> {
                    countAutoDeleteMessages.set(result.data)
                    if (countAutoDeleteMessages.get() > 0) {
                        deleteAutoDeleteMessage()
                    }
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun sendMessageWithoutMedia() {
        viewModelScope.launch {
            val messageList = getMessageRequestModels()
            if (messageList.size == 1) {
                createOrEditMessage(messageList.first())
            } else {
                createMultipleMessages(messageList)
            }
        }
    }

    private fun joinGroupCallUseCase(roomId: String) {
        viewModelScope.launch {
            when (val result = joinGroupCallUseCase.invoke(roomId)) {
                is Result.Success -> {
                    if (result.data.isEmpty()) {
                        // success
                        action(OnCallGroup(roomId))
                    } else {
                        // failed
                        action(
                            OnShowNegativeSnackBar(
                                result.data
                                    .ifEmpty { application.getString(GapoStrings.shared_error_general) }
                            )
                        )
                    }
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    action(
                        OnShowNegativeSnackBar(
                            result.exception.message.orEmpty()
                                .ifEmpty { application.getString(GapoStrings.call_group_join_failed) }
                        )
                    )
                }
            }
        }
    }

    private suspend fun getUsersParticipantsByIdUseCaseForCalling(userId: String): MessageUserModel? {
        return when (
            val result =
                userUseCase.fetchUsersWithIdsUseCase(arrayListOf(userId))
        ) {
            is Result.Success -> {
                result.data.firstOrNull()
            }

            is Result.Error -> {
                Timber.e(result.exception)
                null
            }
        }
    }

    override fun onAppBarGroupCall(roomId: String) {
        onMessengerMessageOnGroupCall(roomId)
    }

    private fun fetchBotCommand(threadId: Long, botId: String, botName: String) {
        viewModelScope.launch(coroutineDispatchers.io) {
            when (val result = fetchBotCommandsUseCase(botId)) {
                is Result.Success -> {
                    if (result.data.isNotEmpty()) {
                        withContext(coroutineDispatchers.main) {
                            action(
                                OnOpenBotCommand(
                                    threadId = threadId,
                                    botId = botId,
                                    botName = botName
                                )
                            )
                        }
                    }
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun submitViolation(body: ReportViolationRequestBody) {
        viewModelScope.launch {
            when (val result = reportViolationMessageUseCase(body)) {
                is Result.Success -> {
                    _reportViolationEventLiveData.value = Event(true)
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    _reportViolationEventLiveData.value = Event(false)
                }
            }
        }
    }

    fun onQuickChatClicked(quickMessage: QuickMessageModel) {
        _viewEvent.value = Event(MessengerViewEvent.OnInputQuickMessages(quickMessage))
    }

    fun onImagesClicked(data: List<String>, index: Int) {
        _viewEvent.value = Event(MessengerViewEvent.OnViewImages(data, index))
    }

    fun getImagesByCommand(command: String, media: String): Pair<List<String>, Int> {
        val quickMessage = quickMessages.findLast { it.command == command }
        val indexOfMedia: Int = quickMessage?.body?.media?.indexOfFirst { it == media } ?: 0
        return Pair<List<String>, Int>(quickMessage?.body?.media.orEmpty(), indexOfMedia)
    }

    fun searchCommand(query: String) {
        if (quickMessages.isEmpty()) {
            getQuickMessages(query)
            return
        }
        if (query.isBlank()) {
            _quickMessagesLiveData.value = quickMessages
        }

        val filteredMessages =
            quickMessages.filter { it.command.contains(query.normalizeString(), ignoreCase = true) }

        _quickMessagesLiveData.value = filteredMessages
        _viewEvent.value = Event(OnOpenQuickMessagesPopUp(query, filteredMessages.size))
    }

    private fun getQuickMessages(query: String) {
        viewModelScope.launch {
            when (val result = fetchQuickMessagesUseCase()) {
                is Result.Error -> {
                    Timber.e(result.exception)
                    _quickMessagesLiveData.value = emptyList()
                }

                is Result.Success -> {
                    quickMessages.clear()
                    quickMessages.addAll(result.data)
                    if (quickMessages.isNotEmpty() && query.isNotEmpty()) {
                        searchCommand(query)
                    } else {
                        _quickMessagesLiveData.value = result.data
                        _viewEvent.value = Event(OnOpenQuickMessagesPopUp(query, result.data.size))
                    }
                }
            }
        }
    }
}
