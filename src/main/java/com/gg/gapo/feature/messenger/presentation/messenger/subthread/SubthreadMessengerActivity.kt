package com.gg.gapo.feature.messenger.presentation.messenger.subthread

import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commitNow
import androidx.lifecycle.lifecycleScope
import com.airbnb.deeplinkdispatch.DeepLinkSpec
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.WebDeepLink
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerMessageDeepLink
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoPlurals
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.bottomsheet.menu.GapoMenuBottomSheetActionItem
import com.gg.gapo.core.ui.bottomsheet.menu.GapoMenuBottomSheetFragment
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbar
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.utilities.bundle.intExtra
import com.gg.gapo.core.utilities.bundle.stringExtra
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.BooleanValue
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationRole
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.presentation.messenger.MessengerFragment
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.SubthreadMessengerActivity.Companion.CONVERSATION_ID_EXTRA
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewModel
import com.gg.gapo.feature.messenger.utils.copy
import com.gg.gapo.feature.messenger.utils.navToMessage
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.SubthreadMessengerActivityBinding
import com.google.android.material.snackbar.BaseTransientBottomBar
import com.r0adkll.slidr.Slidr
import com.r0adkll.slidr.model.SlidrConfig
import com.r0adkll.slidr.model.SlidrInterface
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.getViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

/**
 * <AUTHOR>
 * @since 21/2/2023
 */
@AppDeepLink("messenger/subthread/{$CONVERSATION_ID_EXTRA}")
@WebDeepLink("messenger/subthread/{$CONVERSATION_ID_EXTRA}")
internal class SubthreadMessengerActivity : GapoThemeBaseActivity() {

    private val conversationId by stringExtra(CONVERSATION_ID_EXTRA)
    private val messageId by intExtra(MessengerMessageDeepLink.MESSAGE_ID_EXTRA)
    private val keySearch by stringExtra(MessengerMessageDeepLink.MESSAGE_KEY_SEARCH_EXTRA)

    private val subthreadMessengerViewModel by viewModel<SubthreadMessengerViewModel>()

    private var messengerViewModel: MessengerViewModel? = null

    private lateinit var binding: SubthreadMessengerActivityBinding
    private lateinit var slidr: SlidrInterface

    override fun onCreate(savedInstanceState: Bundle?) {
        overridePendingTransition(
            R.anim.messenger_slide_fade_in_right,
            R.anim.messenger_slide_fade_out_right
        )
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        super.onCreate(savedInstanceState)
        binding = SubthreadMessengerActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        slidr = Slidr.attach(
            this,
            SlidrConfig.Builder().scrimColor(Color.BLACK).scrimStartAlpha(0.8f)
                .scrimEndAlpha(0f)
                .build()
        )
        val conversationId = conversationId?.toLongOrNull() ?: kotlin.run {
            finish()
            return
        }
        if (messageId != -1) {
            subthreadMessengerViewModel.createSubthread(conversationId, messageId)
        } else {
            subthreadMessengerViewModel.fetchConversation(conversationId)
        }

        observe()
    }

    fun isSameConversation(conversationId: String): Boolean {
        return conversationId == subthreadMessengerViewModel.subthreadId?.toString()
    }

    private fun observe() {
        subthreadMessengerViewModel.conversationLiveData.observe(
            this,
            EventObserver {
                initView(it)
            }
        )

        subthreadMessengerViewModel.finishEventLiveData.observe(
            this,
            EventObserver {
                makeNegativeSnackbar(
                    message = getString(GapoStrings.messenger_thread_error),
                    duration = BaseTransientBottomBar.LENGTH_SHORT,
                    isLineLimited = false
                )?.showOnTop()
                lifecycleScope.launch {
                    delay(1500)
                    finish()
                }
            }
        )

        subthreadMessengerViewModel.showMessage.observe(
            this,
            EventObserver {
                makeNormalSnackbar(it)?.show()
            }
        )

        subthreadMessengerViewModel.openSettings.observe(
            this,
            EventObserver { conversation ->
                openSubThreadAction(
                    fragmentManager = <EMAIL>,
                    conversation = conversation,
                    title = getString(GapoStrings.setting_menu_setting),
                    isShowLockSubThread = true,
                    canSendMessage = messengerViewModel?.conversationLiveData?.value?.settings?.disableMemberSendMessage == BooleanValue.FALSE.value
                ) {
                    when (it) {
                        SubThreadSetting.ORIGINAL_MESSAGE -> {
                            navToMessage(
                                threadId = conversation.parentId.toString(),
                                messageId = subthreadMessengerViewModel.rootMessageId.toString()
                            )
                            finish()
                        }
                        SubThreadSetting.NOTIFICATION -> {
                            subthreadMessengerViewModel.toggleNotification {
                            }
                        }
                        SubThreadSetting.LOCK -> {
                            subthreadMessengerViewModel.toggleSubThreadLockOption {
                            }
                        }

                        SubThreadSetting.COPY_LINK_SUB_THREAD -> {
                            this.copy(url = getSubThreadUrl() + subthreadMessengerViewModel.subthreadId)
                        }
                    }
                }
            }
        )
    }

    private fun initView(conversation: ConversationModel) {
        messengerViewModel = getViewModel {
            parametersOf(conversation.id)
        }
        messengerViewModel?.setKeySearch(keySearch)

        messengerViewModel?.lockSlidrEventLiveData?.observe(
            this,
            EventObserver { lock ->
                if (lock) {
                    slidr.lock()
                } else {
                    slidr.unlock()
                }
            }
        )

        with(binding) {
            textName.text = conversation.name
            imageBack.setDebouncedClickListener {
                finish()
            }
            textCancel.setDebouncedClickListener {
                messengerViewModel?.onAppBarOnClickCancelSelection()
            }
            imageSettings.setDebouncedClickListener {
                subthreadMessengerViewModel.subthreadId?.let { conversationId ->
                    subthreadMessengerViewModel.openSettings(conversationId)
                }
            }
        }

        supportFragmentManager.commitNow(true) {
            replace(R.id.fragment_container, MessengerFragment.newInstance(conversation.id))
        }

        messengerViewModel?.selectedMessageLiveData?.observe(this) { messages ->
            binding.layoutAppBar.isVisible = messages.isEmpty()
            binding.layoutSelection.isVisible = messages.isNotEmpty()
            if (messages.isNotEmpty()) {
                binding.textMessageCount.text = GapoGlobalResources.getQuantityString(
                    GapoPlurals.messenger_plurals_forward_selection_toolbar_title,
                    messages.size,
                    messages.size
                )
            }
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(
            R.anim.messenger_slide_fade_in_right,
            R.anim.messenger_slide_fade_out_right
        )
    }

    companion object {
        internal const val CONVERSATION_ID_EXTRA = "conversation_id"

        private val webDeepLinkPrefix: String
            get() = WebDeepLink::class.annotations.filterIsInstance<DeepLinkSpec>()
                .first().prefix.first()

        fun getWebDeepLinkUrl(): String {
            val webDeepLink = SubthreadMessengerActivity::class.java.getAnnotation(WebDeepLink::class.java)
            return if (webDeepLink != null) {
                val path = webDeepLinkPrefix + webDeepLink.value.firstOrNull()?.replace("{$CONVERSATION_ID_EXTRA}", "")
                path
            } else {
                ""
            }
        }

        fun getSubThreadUrl(): String {
            val webDeepLink = SubthreadMessengerActivity::class.java.getAnnotation(WebDeepLink::class.java)
            return if (webDeepLink != null) {
                val path = webDeepLinkPrefix + "messenger/"
                path
            } else {
                ""
            }
        }
    }
}

internal enum class SubThreadSetting {
    ORIGINAL_MESSAGE, NOTIFICATION, LOCK, COPY_LINK_SUB_THREAD;
}

internal fun Context.openSubThreadAction(
    fragmentManager: FragmentManager,
    conversation: ConversationModel,
    title: String,
    isShowLockSubThread: Boolean,
    canSendMessage: Boolean = true,
    callBack: (SubThreadSetting) -> Unit
) {
    with(this) {
        GapoMenuBottomSheetFragment.start(
            menu = arrayListOf<GapoMenuBottomSheetActionItem>().apply {
                val originMessage = GapoMenuBottomSheetActionItem(
                    id = SubThreadSetting.ORIGINAL_MESSAGE.ordinal,
                    title = getString(GapoStrings.messenger_go_to_original_message),
                    imageStart = GapoDrawables.ic24_line15_bubble_rectangle_arrowright
                )
                val notification = GapoMenuBottomSheetActionItem(
                    id = SubThreadSetting.NOTIFICATION.ordinal,
                    title = getString(
                        if (conversation.isTurnOffNotification) {
                            GapoStrings.notification_setting_turn_on
                        } else GapoStrings.notification_setting_turn_off
                    ),
                    imageStart = if (conversation.isTurnOffNotification) {
                        GapoDrawables.ic24_fill_bell
                    } else GapoDrawables.ic24_fill_bell_slash,
                    imageStartInt = ContextCompat.getColor(this@with, GapoColors.contentPrimary)
                )

                val copyLinkSubThread = GapoMenuBottomSheetActionItem(
                    id = SubThreadSetting.COPY_LINK_SUB_THREAD.ordinal,
                    title = getString(
                        GapoStrings.common_share_text_copy
                    ),
                    imageStart = GapoDrawables.ic24_line15_link,
                    imageStartInt = ContextCompat.getColor(this@with, GapoColors.contentPrimary)
                )

                this.add(originMessage)
                this.add(notification)
                if (isShowLockSubThread && (conversation.role == ConversationRole.OWNER || conversation.role == ConversationRole.ADMIN) && (conversation.parentThreadType == ConversationType.GROUP || conversation.type == ConversationType.GROUP)) {
                    val subThreadOptionLockEnable = GapoMenuBottomSheetActionItem(
                        id = SubThreadSetting.LOCK.ordinal,
                        title = getString(
                            if (canSendMessage) {
                                GapoStrings.messenger_sub_thread_lock_option_lock
                            } else GapoStrings.messenger_sub_thread_lock_option_unlock
                        ),
                        imageStart = if (canSendMessage) {
                            GapoDrawables.ic24_line15_lock
                        } else GapoDrawables.ic24_line15_unlock,
                        imageStartInt = ContextCompat.getColor(this@with, GapoColors.contentPrimary)
                    )
                    this.add(subThreadOptionLockEnable)
                }
                if (conversation.isSubthread) {
                    this.add(copyLinkSubThread)
                }
            },
            title = title,
            titleGravity = Gravity.START,
            listener = object :
                GapoMenuBottomSheetFragment.GapoMenuBottomSheetFragmentListener {
                override fun onClickedMenuItem(
                    item: GapoMenuBottomSheetActionItem,
                    position: Int
                ) {
                    callBack.invoke(SubThreadSetting.entries[item.id])
                }
            },
            fragmentManager = fragmentManager
        )
    }
}
